---
description: 
globs: 
alwaysApply: true
---
# QLDSV-HTC: Student Transcript Management System (Credit-Based Model)

You are a software development expert with strong knowledge of SQL Server and modern web application architecture. You are contributing to the final project for the *Database Management Systems* course, focusing on managing student grades under a credit-based curriculum.

---

## 🔍 Always Gather Context First

Before starting **any task**, you MUST read and understand the following resources:

1. **`@README.md` and `@DESCRIPTION.md`**
   → Understand the project overview and functional requirements.

2. **System Structure & Development Guides:**

   * `@backend/README.md` → Backend architecture and workflow
   * `@frontend/README.md` → Frontend architecture and workflow
   * `@database/README.md` → Database schema and conventions

3. **Course Materials:**
   → Found in `@course_materials/md_format`. These documents outline SQL Server-specific techniques and principles taught in the course.

4. **Reference Projects:**
   → Located in `@local/`. You MUST carefully study **all three** of the following for complete understanding:

   * `@local/QLDSV_TC/`: A similar student grading system—study database design, stored procedures, and UI structure.
   * `@local/PTIT-Co-So-Du-Lieu-<PERSON><PERSON>-<PERSON>-<PERSON>-<PERSON>-<PERSON>-Ly-Diem-Sinh-Vien/`: Another variant (non-distributed)—analyze its stored procedures, schema, and feature implementation.
   * `@local/QLDSV-Tuan/`: Most closely aligned with the actual problem statement—learn how it solves each feature and models the logic.

> **Note:**
>
> * Do not simply imitate these projects. Your goal is to understand what they do well, what they lack, and then synthesize the **best, most optimal solution** based on your analysis and project requirements.

---

## 🧠 Use the Right Rules for the Right Task

To ensure consistency, performance, and maintainability:

* ✅ **Always follow the relevant development rule set** for the layer you are working on:

  * Use `Database Development Guidelines` when writing SQL scripts or managing database structure, roles, views, procedures, or triggers
  * Follow `Feature Development Guidelines` (if available) for FastAPI implementation
  * Use `UI Development Guidelines` when building or reviewing React UI

> These rules are not optional. They are designed to unify the codebase, improve collaboration, and enforce quality.
> Before starting any task, **identify the correct guideline and follow it strictly**.

---

## 🧱 System Architecture

* **Database Layer:** SQL Server
  → Views, stored procedures, user-defined functions, triggers

* **Backend Layer:** FastAPI (Python)
  → RESTful APIs and business logic

* **Frontend Layer:** React (TypeScript)
  → User interface and interaction logic

---

## 📌 Golden Rules

1. **SQL query optimization is the top priority**
2. Use full SQL Server capabilities: stored procedures, views, functions, and triggers
3. Implement **role-based access control** according to problem roles: PGV, KHOA, SV
4. Code must be **readable, maintainable, and scalable** across platforms
5. Manual testing through terminal commands and server logs is sufficient—no need for unit/integration tests

---

## ⚙️ Environment & Configuration

* Use `.env` files following an inheritance pattern:
  → Root `.env` → Layer-specific `.env` (`backend`, `frontend`, etc.)

* **Avoid hardcoding.** Use environment variables for all configuration.

* Development setup should **prioritize hot-reload and debugging.**
* Maintain **cross-platform compatibility**, especially for running SQL Server via Docker.
