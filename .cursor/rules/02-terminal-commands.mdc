---
description: 
globs: 
alwaysApply: true
---
# **Terminal commands Guidelines**

* After completing any code changes or fixes, always run the `run_terminal_cmd` tool with the parameter `is_background=false`:

  * You **must always** run terminal commands with `is_background=false` so that you can see server logs and identify any runtime errors.
  * **The server is always running in the background and updates in real-time with your code.** Therefore, errors related to port usage are normal when running bash scripts — ignore these and focus on other errors. Your task is to investigate and resolve all other issues. If there are **no other errors**, it means everything is working, and you've successfully updated the background server manually.

* Interact with the server using the following Bash scripts:

---

## **1. Database (`start-database.sh`)**

| Command                                | Description                                                      |
| -------------------------------------- | ---------------------------------------------------------------- |
| `./scripts/start-database.sh --setup`  | Creates a Docker network, pulls the image, then starts the DB.   |
| `./scripts/start-database.sh`          | Starts an existing database container.                           |
| `./scripts/start-database.sh --reset`  | **Deletes all data**, then recreates and restarts the DB.        |
| `./scripts/start-database.sh --delete` | **Deletes all data and container**. Does **not** restart the DB. |

---

## **2. Backend (`start-backend.sh`)**

| Command                                 | Description                                                                      |
| --------------------------------------- | -------------------------------------------------------------------------------- |
| `./scripts/start-backend.sh --setup`    | Creates a Python virtual environment, installs packages, then starts the server. |
| `./scripts/start-backend.sh`            | Activates the environment and starts the FastAPI server.                         |
| `./scripts/start-backend.sh --reset`    | Deletes and recreates the environment, then restarts the server.                 |
| `./scripts/start-backend.sh --reset-db` | **(Special)** Triggers a database reset within the application.                  |
| `./scripts/start-backend.sh --delete`   | Deletes the environment and logs. Does **not** restart the server.               |

---

## **3. Frontend (`start-frontend.sh`)**

| Command                                | Description                                                              |
| -------------------------------------- | ------------------------------------------------------------------------ |
| `./scripts/start-frontend.sh --setup`  | Runs `npm install`, creates a `.env` file, then starts the Vite server.  |
| `./scripts/start-frontend.sh`          | Starts the Vite development server.                                      |
| `./scripts/start-frontend.sh --reset`  | Deletes `node_modules`, reinstalls dependencies, then starts the server. |
| `./scripts/start-frontend.sh --delete` | Deletes `node_modules` and logs. Does **not** restart the server.        |

---

### **Notes**

* Database scripts only manage the SQL Server container environment — you generally **don't need to use them** unless you're working directly with the database.
* You usually **don’t need to run** these bash scripts because the background servers automatically reflect code changes in real-time. Only run the scripts in the following cases:

  * **To verify a successful build**: Run `./scripts/start-backend.sh` or `./scripts/start-frontend.sh`.
  * **When modifying `.sql` files in `@database/`**: Run `./scripts/start-backend.sh --reset-db` to drop and recreate the database.
  * **When changing dependencies**: Run `./scripts/start-backend.sh --setup` or `./scripts/start-frontend.sh --setup` to install packages.