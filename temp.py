import pyodbc

# <PERSON><PERSON><PERSON> tra driver sẵn có
print(pyodbc.drivers())

# Thiết lập kết nối tới SQL Server
conn = pyodbc.connect(
    "DRIVER={ODBC Driver 18 for SQL Server};"
    "SERVER=127.0.0.1,1434;"
    "DATABASE=master;"
    "UID=sa;"
    "PWD=P@ssw0rd!QLDSV;"
    "TrustServerCertificate=yes"
)

# Khởi tạo cursor
cursor = conn.cursor()

# Script SQL đã được điền thông tin cụ thể
sql_script = """
-- Tạo login cho ứng dụng QLDSV_HTC
USE [master];

IF EXISTS (SELECT * FROM sys.server_principals WHERE name = 'qlapp_user')
    DROP LOGIN [qlapp_user];
CREATE LOGIN [qlapp_user] WITH PASSWORD = 'App123@pass';

IF EXISTS (SELECT * FROM sys.server_principals WHERE name = 'pgv_user')
    DROP LOGIN [pgv_user];
CREATE LOGIN [pgv_user] WITH PASSWORD = 'PGV123@pass';

IF EXISTS (SELECT * FROM sys.server_principals WHERE name = 'khoa_user')
    DROP LOGIN [khoa_user];
CREATE LOGIN [khoa_user] WITH PASSWORD = 'KHOA123@pass';

IF EXISTS (SELECT * FROM sys.server_principals WHERE name = 'sv_user')
    DROP LOGIN [sv_user];
CREATE LOGIN [sv_user] WITH PASSWORD = 'SV123@pass';
"""

# Tách và thực thi từng câu lệnh riêng biệt (vì pyodbc không hỗ trợ GO)
sql_statements = [stmt.strip() for stmt in sql_script.strip().split('\n') if stmt.strip() and not stmt.strip().startswith('--')]
current_statement = ""

try:
    for line in sql_statements:
        if line.upper() == "GO":
            if current_statement:
                cursor.execute(current_statement)
                current_statement = ""
        else:
            current_statement += f"{line} "
    if current_statement:
        cursor.execute(current_statement)
    conn.commit()
    print("✅ Script thực thi thành công.")
except pyodbc.Error as e:
    print("❌ Lỗi khi thực thi script:", e)


# Thực thi câu lệnh cuối cùng nếu còn
if current_statement:
    cursor.execute(current_statement)

# Lưu thay đổi
conn.commit()

# Ví dụ: thử đăng nhập bằng tài khoản sv_user
try:
    test_conn = pyodbc.connect(
        "DRIVER={ODBC Driver 18 for SQL Server};"
        "SERVER=127.0.0.1,1434;"
        "DATABASE=master;"
        "UID=sv_user;"
        "PWD=SV123@pass;"
        "TrustServerCertificate=yes"
    )
    print("✅ Đăng nhập sv_user thành công.")
    test_conn.close()
except pyodbc.Error as e:
    print("❌ Không thể đăng nhập với sv_user:", e)


print("Logins đã được tạo thành công.")

# Đóng kết nối
conn.close()
