"""Application entry point.

This file is intentionally minimal. All heavy lifting is delegated to
`app.core` modules to keep the bootstrap clean.
"""

import uvicorn

from app.core.application import create_app
from app.core.config import verify_cwd, APP_SETTINGS

# Verify working directory before doing anything else
verify_cwd()

# Create the FastAPI application
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=APP_SETTINGS.APP_HOST,
        port=APP_SETTINGS.APP_PORT,
        reload=APP_SETTINGS.APP_RELOAD
    )
