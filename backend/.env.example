# =============================================
# Backend Service Configuration (Example)
# =============================================

# Inherit all global variables from project root `.env`
include=../.env

# ---------------------------------------------------------------------
# ⚙️ FastAPI Application Settings
# ---------------------------------------------------------------------
# These variables configure the FastAPI/UVicorn server.  All have sane
# defaults in code, but you can override them per-environment here.

APP_TITLE="QLDSV-HTC API"
APP_DESCRIPTION="Simple API for QLDSV-HTC"
APP_VERSION="1.0.0"

# Server host/port (overrides BACKEND_HOST/BACKEND_PORT if both provided)
APP_HOST=0.0.0.0
APP_PORT=8000

# Auto-reload source files in development
APP_RELOAD=true

# ---------------------------------------------------------------------
# 🔧 API Configuration
# ---------------------------------------------------------------------

API_PREFIX=/api/v1

# ---------------------------------------------------------------------
# 🐍 Uvicorn / FastAPI Flags
# ---------------------------------------------------------------------

# Enable verbose debug logs (true|false)
FASTAPI_DEBUG=true
# Enable live-reload (true|false).  If set, overrides APP_RELOAD.
FASTAPI_RELOAD=true

# ---------------------------------------------------------------------
# 📜 Logging
# ---------------------------------------------------------------------
# LOG_LEVEL is inherited from root .env but can be overridden here
# LOG_LEVEL=DEBUG

# ---------------------------------------------------------------------
# 🛠️ Additional backend-specific configuration keys can be added below
# ---------------------------------------------------------------------

