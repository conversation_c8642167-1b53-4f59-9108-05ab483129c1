"""Database connection utilities."""

import os
import logging
from typing import Dict, Any, List, Optional

import pyodbc
from dotenv import load_dotenv

from app.core.config import APP_SETTINGS

logger = logging.getLogger("app.db.connection")


def get_available_drivers() -> List[str]:
    """Get available ODBC drivers."""
    return pyodbc.drivers()


def normalize_driver_name(driver: str) -> str:
    """Normalize driver name by removing surrounding quotes if present."""
    if driver.startswith('"') and driver.endswith('"'):
        return driver[1:-1]
    return driver


def _build_conn_str(user: str, pwd: str, db: str) -> str:  # noqa: WPS430 – nested helper is fine here
    """Return pyodbc-formatted connection string (internal use)."""
    db_host = APP_SETTINGS.DB_HOST
    db_port = APP_SETTINGS.DB_PORT
    db_driver = normalize_driver_name(APP_SETTINGS.DB_DRIVER)
    return (
        f"DRIVER={{{db_driver}}};"
        f"SERVER={db_host},{db_port};"
        f"DATABASE={db};"
        f"UID={user};"
        f"PWD={pwd};"
        "TrustServerCertificate=yes;"
        "Connection Timeout=2;"
    )


def build_connection_string(user: str, password: str, database: Optional[str] = None) -> str:  # noqa: D401
    """Public helper to build a connection string for arbitrary credentials."""
    if database is None:
        database = APP_SETTINGS.DB_NAME
    return _build_conn_str(user, password, database)


def get_connection_string(database: Optional[str] = None) -> str:
    """Generate a *sa* connection string for SQL Server (legacy helper)."""
    db_name = APP_SETTINGS.DB_NAME
    sa_password = APP_SETTINGS.MSSQL_SA_PASSWORD

    if database is None:
        database = db_name

    return _build_conn_str("sa", sa_password, database)


def get_connection(database: Optional[str] = None) -> pyodbc.Connection:
    """Get a database connection."""
    connection_string = get_connection_string(database)
    try:
        logger.debug(
            f"Attempting to connect with string: {connection_string.split('PWD=')[0]}PWD=*****")
        return pyodbc.connect(connection_string)
    except Exception as e:
        logger.error(f"Failed to connect to database: {str(e)}")
        logger.error(
            f"Connection string (without password): {connection_string.split('PWD=')[0]}PWD=*****")

        # Check if the driver exists
        available_drivers = get_available_drivers()
        db_driver = normalize_driver_name(APP_SETTINGS.DB_DRIVER)

        if db_driver not in available_drivers:
            logger.error(
                f"Driver '{db_driver}' not found. Available drivers: {available_drivers}")

        # Try with exact connection string from temp.py as a last resort
        try:
            logger.debug(
                "Attempting fallback connection with ODBC Driver 18...")
            fallback_connection_string = (
                "DRIVER={ODBC Driver 18 for SQL Server};"
                "SERVER=127.0.0.1,1434;"
                f"DATABASE={database or 'master'};"
                "UID=sa;"
                "PWD=P@ssw0rd!QLDSV;"
                "TrustServerCertificate=yes;"
                "Connection Timeout=2;"
            )
            return pyodbc.connect(fallback_connection_string)
        except Exception as inner_e:
            logger.error(f"Fallback connection also failed: {str(inner_e)}")
            raise e  # Raise the original error


def check_db_health() -> Dict[str, Any]:
    """Check database health and return status information."""
    try:
        conn = get_connection("master")
        cursor = conn.cursor()

        # Check if server is responsive
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()[0]

        # Get list of databases
        cursor.execute("SELECT name FROM sys.databases")
        databases = [row[0] for row in cursor.fetchall()]

        conn.close()

        # Get connection info for response
        host = APP_SETTINGS.DB_HOST
        port = APP_SETTINGS.DB_PORT

        # Fix driver string for display
        driver = normalize_driver_name(APP_SETTINGS.DB_DRIVER)

        return {
            "status": "healthy",
            "message": "Database connection successful",
            "version": version,
            "databases": databases,
            "connection": {
                "host": f"{host}:{port}",
                "driver": driver,
                "user": "sa"
            }
        }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")

        # Get connection info for response
        host = APP_SETTINGS.DB_HOST
        port = APP_SETTINGS.DB_PORT

        # Fix driver string for display
        driver = normalize_driver_name(APP_SETTINGS.DB_DRIVER)

        return {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
            "connection": {
                "host": f"{host}:{port}",
                "driver": driver,
                "user": "sa"
            }
        }
