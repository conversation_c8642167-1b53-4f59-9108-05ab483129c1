"""API router configuration."""

from fastapi import APIRouter

from app.api.endpoints import health

# Create the main API router without prefix (prefix is added in application.py)
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, prefix="/system", tags=["Health"])

# Add more routers here as needed
# api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
# api_router.include_router(users.router, prefix="/users", tags=["Users"])
