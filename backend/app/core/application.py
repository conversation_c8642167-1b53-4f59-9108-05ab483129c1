"""FastAPI application factory."""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.router import api_router
from app.core import config, logger


def create_app() -> FastAPI:
    """Build and return a configured FastAPI app instance."""

    # Set up logging
    app_logger = logger.setup_logging(config.APP_SETTINGS)

    app = FastAPI(
        title=config.APP_SETTINGS.APP_TITLE,
        description=config.APP_SETTINGS.APP_DESCRIPTION,
        version=config.APP_SETTINGS.APP_VERSION,
    )

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Root endpoint only
    @app.get("/")
    async def root():
        return {"message": f"Welcome to {config.APP_SETTINGS.APP_TITLE}"}

    @app.get("/test")
    def _demo():
        return {
            "status": "ok",
            "project_name": config.APP_SETTINGS.PROJECT_NAME,
            "db_host": config.APP_SETTINGS.DB_HOST,
            "db_port": config.APP_SETTINGS.DB_PORT,
            "db_name": config.APP_SETTINGS.DB_NAME,
            "db_driver": config.APP_SETTINGS.DB_DRIVER,
            "sa_password": config.APP_SETTINGS.MSSQL_SA_PASSWORD,
            "mssql_app_user": config.APP_SETTINGS.MSSQL_APP_USER,
            "mssql_app_password": config.APP_SETTINGS.MSSQL_APP_PASSWORD,
            "mssql_pgv_user": config.APP_SETTINGS.MSSQL_PGV_USER,
            "mssql_pgv_password": config.APP_SETTINGS.MSSQL_PGV_PASSWORD,
            "mssql_khoa_user": config.APP_SETTINGS.MSSQL_KHOA_USER,
            "mssql_khoa_password": config.APP_SETTINGS.MSSQL_KHOA_PASSWORD,
            "mssql_sv_user": config.APP_SETTINGS.MSSQL_SV_USER,
            "mssql_sv_password": config.APP_SETTINGS.MSSQL_SV_PASSWORD,
            "app_title": config.APP_SETTINGS.APP_TITLE,
            "app_description": config.APP_SETTINGS.APP_DESCRIPTION,
            "app_version": config.APP_SETTINGS.APP_VERSION,
            "app_host": config.APP_SETTINGS.APP_HOST,
            "app_port": config.APP_SETTINGS.APP_PORT,
            "app_reload": config.APP_SETTINGS.APP_RELOAD,
            "api_prefix": config.APP_SETTINGS.API_PREFIX,
        }

    # Include API router with prefix from config
    app.include_router(
        api_router,
        prefix=config.APP_SETTINGS.API_PREFIX
    )

    app_logger.info(
        f"Application created with API prefix: {config.APP_SETTINGS.API_PREFIX}")

    return app
