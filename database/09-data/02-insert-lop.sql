-- ===============================================
-- QLDSV-HTC Database Data
-- File: 02-insert-lop.sql
-- Description: Insert dữ liệu cho bảng LOP (25-30 lớp)
-- ===============================================

USE [$(DB_NAME)];
GO

-- Xóa dữ liệu cũ (nếu có)
DELETE FROM [dbo].[LOP];
GO

-- Insert dữ liệu lớp miền Bắc - Khóa 2021-2025
INSERT INTO [dbo].[LOP] ([MALOP], [TENLOP], [KHOAHOC], [MAKHOA]) VALUES
('CN21B01', N'D21CQCN01-B', '2021-2025', 'BVH_CNTT1'),
('CN21B02', N'D21CQCN02-B', '2021-2025', 'BVH_CNTT1'),
('VT21B01', N'D21CQVT01-B', '2021-2025', 'BVH_VT1'),
('VT21B02', N'D21CQVT02-B', '2021-2025', 'BVH_VT1'),
('KT21B01', N'D21CQKT01-B', '2021-2025', 'BVH_KTDT1'),
('QT21B01', N'D21CQQT01-B', '2021-2025', 'BVH_QTKD1'),
('AT21B01', N'D21CQAT01-B', '2021-2025', 'BVH_ATTT'),
('DP21B01', N'D21CQDP01-B', '2021-2025', 'BVH_DPT');
GO

-- Insert dữ liệu lớp miền Bắc - Khóa 2022-2026  
INSERT INTO [dbo].[LOP] ([MALOP], [TENLOP], [KHOAHOC], [MAKHOA]) VALUES
('CN22B01', N'D22CQCN01-B', '2022-2026', 'BVH_CNTT1'),
('CN22B02', N'D22CQCN02-B', '2022-2026', 'BVH_CNTT1'),
('VT22B01', N'D22CQVT01-B', '2022-2026', 'BVH_VT1'),
('VT22B02', N'D22CQVT02-B', '2022-2026', 'BVH_VT1'),
('KT22B01', N'D22CQKT01-B', '2022-2026', 'BVH_KTDT1'),
('QT22B01', N'D22CQQT01-B', '2022-2026', 'BVH_QTKD1'),
('AT22B01', N'D22CQAT01-B', '2022-2026', 'BVH_ATTT'),
('DP22B01', N'D22CQDP01-B', '2022-2026', 'BVH_DPT');
GO

-- Insert dữ liệu lớp miền Nam - Khóa 2021-2025
INSERT INTO [dbo].[LOP] ([MALOP], [TENLOP], [KHOAHOC], [MAKHOA]) VALUES
('CN21N01', N'D21CQCN01-N', '2021-2025', 'BVS_CNTT2'),
('CN21N02', N'D21CQCN02-N', '2021-2025', 'BVS_CNTT2'),
('VT21N01', N'D21CQVT01-N', '2021-2025', 'BVS_VT2'),
('KT21N01', N'D21CQKT01-N', '2021-2025', 'BVS_KTDT2'),
('QT21N01', N'D21CQQT01-N', '2021-2025', 'BVS_QTKD2');
GO

-- Insert dữ liệu lớp miền Nam - Khóa 2022-2026
INSERT INTO [dbo].[LOP] ([MALOP], [TENLOP], [KHOAHOC], [MAKHOA]) VALUES
('CN22N01', N'D22CQCN01-N', '2022-2026', 'BVS_CNTT2'),
('CN22N02', N'D22CQCN02-N', '2022-2026', 'BVS_CNTT2'),
('VT22N01', N'D22CQVT01-N', '2022-2026', 'BVS_VT2'),
('KT22N01', N'D22CQKT01-N', '2022-2026', 'BVS_KTDT2'),
('QT22N01', N'D22CQQT01-N', '2022-2026', 'BVS_QTKD2');
GO

-- Insert dữ liệu lớp miền Bắc - Khóa 2023-2027 (sinh viên năm nhất)
INSERT INTO [dbo].[LOP] ([MALOP], [TENLOP], [KHOAHOC], [MAKHOA]) VALUES
('CN23B01', N'D23CQCN01-B', '2023-2027', 'BVH_CNTT1'),
('CN23B02', N'D23CQCN02-B', '2023-2027', 'BVH_CNTT1'),
('VT23B01', N'D23CQVT01-B', '2023-2027', 'BVH_VT1'),
('KT23B01', N'D23CQKT01-B', '2023-2027', 'BVH_KTDT1');
GO

-- Insert dữ liệu lớp miền Nam - Khóa 2023-2027 (sinh viên năm nhất)  
INSERT INTO [dbo].[LOP] ([MALOP], [TENLOP], [KHOAHOC], [MAKHOA]) VALUES
('CN23N01', N'D23CQCN01-N', '2023-2027', 'BVS_CNTT2'),
('VT23N01', N'D23CQVT01-N', '2023-2027', 'BVS_VT2'),
('QT23N01', N'D23CQQT01-N', '2023-2027', 'BVS_QTKD2');
GO

PRINT 'Đã insert thành công ' + CAST(@@ROWCOUNT AS VARCHAR) + ' records vào bảng LOP'; 