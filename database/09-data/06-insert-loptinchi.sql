-- ===============================================
-- QLDSV-HTC Database Data
-- File: 06-insert-loptinchi.sql
-- Description: Insert dữ liệu cho bảng LOPTINCHI (140 lớp tín chỉ)
-- Generated by: 06-generate-loptinchi.py
-- ===============================================

USE [$(DB_NAME)];
GO

-- Xóa dữ liệu cũ (nếu có)
DELETE FROM [dbo].[LOPTINCHI];
GO

-- INSERT LỚP TÍN CHỈ NIÊN KHÓA 2020-2021
INSERT INTO [dbo].[LOPTINCHI] ([NIENKHOA], [HOCKY], [MAMH], [NHOM], [MAGV], [MAKHOA], [SOSVTOITHIEU], [HUYLOP]) VALUES
('2020-2021', 2, 'BUS010', 1, 'GV044', 'BVH_QTKD1', 11, 0),
('2020-2021', 2, 'CSE014', 1, 'GV026', 'BVH_CNTT1', 20, 0),
('2020-2021', 2, 'CSE014', 2, 'GV001', 'BVH_CNTT1', 20, 0),
('2020-2021', 2, 'CSE014', 3, 'GV003', 'BVH_CNTT1', 12, 0),
('2020-2021', 2, 'CSE004', 1, 'GV006', 'BVH_CNTT1', 13, 0),
('2020-2021', 2, 'CSE004', 2, 'GV002', 'BVH_CNTT1', 12, 0),
('2020-2021', 2, 'CSE004', 3, 'GV016', 'BVH_CNTT1', 10, 0),
('2020-2021', 2, 'MIL001', 1, 'GV049', 'BVH_CB1', 24, 0),
('2020-2021', 2, 'TEL009', 1, 'GV040', 'BVH_VT1', 18, 0),
('2020-2021', 2, 'TEL009', 2, 'GV040', 'BVH_VT1', 18, 0),
('2020-2021', 3, 'BUS010', 1, 'GV044', 'BVH_QTKD1', 12, 0),
('2020-2021', 3, 'BUS010', 2, 'GV044', 'BVH_QTKD1', 13, 0),
('2020-2021', 3, 'BUS010', 3, 'GV044', 'BVH_QTKD1', 12, 0),
('2020-2021', 3, 'CSE014', 1, 'GV014', 'BVH_CNTT1', 14, 0),
('2020-2021', 3, 'CSE004', 1, 'GV036', 'BVH_CNTT1', 13, 0),
('2020-2021', 1, 'BUS010', 1, 'GV044', 'BVH_QTKD1', 12, 0),
('2020-2021', 1, 'CSE014', 1, 'GV007', 'BVH_CNTT1', 10, 0),
('2020-2021', 1, 'CSE004', 1, 'GV023', 'BVH_CNTT1', 16, 0),
('2020-2021', 1, 'MIL001', 1, 'GV073', 'BVS_CB2', 19, 0),
('2020-2021', 1, 'TEL009', 1, 'GV040', 'BVH_VT1', 10, 0),
('2020-2021', 1, 'PHI005', 1, 'GV074', 'BVS_CB2', 16, 0),
('2020-2021', 1, 'CSE002', 1, 'GV016', 'BVH_CNTT1', 15, 0),
('2020-2021', 1, 'CSE006', 1, 'GV008', 'BVH_CNTT1', 10, 0),
('2020-2021', 1, 'BUS005', 1, 'GV043', 'BVH_QTKD1', 12, 0),
('2020-2021', 1, 'BUS005', 2, 'GV044', 'BVH_QTKD1', 16, 0),
('2020-2021', 1, 'BUS005', 3, 'GV044', 'BVH_QTKD1', 13, 0),
('2020-2021', 1, 'CSE015', 1, 'GV009', 'BVH_CNTT1', 15, 0),
('2020-2021', 1, 'PHY001', 1, 'GV073', 'BVS_CB2', 20, 1),
('2020-2021', 1, 'ELE005', 1, 'GV049', 'BVH_CB1', 17, 0),
('2020-2021', 1, 'MED005', 1, 'GV048', 'BVH_DPT', 17, 0),
('2020-2021', 1, 'TEL008', 1, 'GV040', 'BVH_VT1', 10, 0);
GO

-- INSERT LỚP TÍN CHỈ NIÊN KHÓA 2021-2022
INSERT INTO [dbo].[LOPTINCHI] ([NIENKHOA], [HOCKY], [MAMH], [NHOM], [MAGV], [MAKHOA], [SOSVTOITHIEU], [HUYLOP]) VALUES
('2021-2022', 1, 'ELE005', 1, 'GV049', 'BVH_CB1', 17, 0),
('2021-2022', 1, 'ELE005', 2, 'GV049', 'BVH_CB1', 16, 0),
('2021-2022', 1, 'ELE005', 3, 'GV050', 'BVH_CB1', 22, 0),
('2021-2022', 1, 'CSE006', 1, 'GV024', 'BVH_CNTT1', 19, 0),
('2021-2022', 1, 'CSE006', 2, 'GV035', 'BVH_CNTT1', 15, 0),
('2021-2022', 1, 'CSE006', 3, 'GV001', 'BVH_CNTT1', 20, 0),
('2021-2022', 1, 'SEC003', 1, 'GV046', 'BVH_ATTT', 12, 0),
('2021-2022', 1, 'CSE010', 1, 'GV007', 'BVH_CNTT1', 17, 0),
('2021-2022', 1, 'BUS012', 1, 'GV044', 'BVH_QTKD1', 15, 0),
('2021-2022', 1, 'CSE012', 1, 'GV011', 'BVH_CNTT1', 10, 0),
('2021-2022', 1, 'MAT005', 1, 'GV049', 'BVH_CB1', 20, 0),
('2021-2022', 1, 'PHY002', 1, 'GV075', 'BVS_CB2', 17, 0),
('2021-2022', 1, 'ENG002', 1, 'GV050', 'BVH_CB1', 20, 0),
('2021-2022', 1, 'ENG001', 1, 'GV050', 'BVH_CB1', 25, 0),
('2021-2022', 1, 'SEC006', 1, 'GV045', 'BVH_ATTT', 20, 0),
('2021-2022', 1, 'ELE003', 1, 'GV073', 'BVS_CB2', 21, 0),
('2021-2022', 2, 'ELE005', 1, 'GV073', 'BVS_CB2', 18, 0),
('2021-2022', 2, 'CSE006', 1, 'GV015', 'BVH_CNTT1', 15, 0),
('2021-2022', 2, 'SEC003', 1, 'GV046', 'BVH_ATTT', 12, 0),
('2021-2022', 2, 'CSE010', 1, 'GV017', 'BVH_CNTT1', 12, 0),
('2021-2022', 2, 'BUS012', 1, 'GV043', 'BVH_QTKD1', 12, 0),
('2021-2022', 2, 'CSE012', 1, 'GV023', 'BVH_CNTT1', 19, 0),
('2021-2022', 2, 'MAT005', 1, 'GV074', 'BVS_CB2', 18, 0),
('2021-2022', 2, 'PHY002', 1, 'GV050', 'BVH_CB1', 18, 0),
('2021-2022', 2, 'PHY002', 2, 'GV050', 'BVH_CB1', 20, 0),
('2021-2022', 2, 'PHY002', 3, 'GV075', 'BVS_CB2', 19, 1),
('2021-2022', 3, 'ELE005', 1, 'GV074', 'BVS_CB2', 21, 0),
('2021-2022', 3, 'CSE006', 1, 'GV011', 'BVH_CNTT1', 20, 0),
('2021-2022', 3, 'CSE006', 2, 'GV030', 'BVH_CNTT1', 19, 1),
('2021-2022', 3, 'CSE006', 3, 'GV006', 'BVH_CNTT1', 13, 1),
('2021-2022', 3, 'SEC003', 1, 'GV045', 'BVH_ATTT', 19, 0);
GO

-- INSERT LỚP TÍN CHỈ NIÊN KHÓA 2022-2023
INSERT INTO [dbo].[LOPTINCHI] ([NIENKHOA], [HOCKY], [MAMH], [NHOM], [MAGV], [MAKHOA], [SOSVTOITHIEU], [HUYLOP]) VALUES
('2022-2023', 2, 'CSE004', 1, 'GV037', 'BVH_CNTT1', 14, 0),
('2022-2023', 2, 'CSE009', 1, 'GV021', 'BVH_CNTT1', 13, 0),
('2022-2023', 2, 'CSE009', 2, 'GV026', 'BVH_CNTT1', 15, 1),
('2022-2023', 2, 'CSE009', 3, 'GV012', 'BVH_CNTT1', 15, 0),
('2022-2023', 2, 'SEC003', 1, 'GV046', 'BVH_ATTT', 16, 0),
('2022-2023', 2, 'PHI004', 1, 'GV050', 'BVH_CB1', 24, 0),
('2022-2023', 2, 'TEL002', 1, 'GV039', 'BVH_VT1', 19, 0),
('2022-2023', 2, 'TEL002', 2, 'GV039', 'BVH_VT1', 10, 0),
('2022-2023', 2, 'TEL002', 3, 'GV040', 'BVH_VT1', 16, 0),
('2022-2023', 2, 'ELE004', 1, 'GV075', 'BVS_CB2', 17, 0),
('2022-2023', 2, 'TEL006', 1, 'GV040', 'BVH_VT1', 10, 0),
('2022-2023', 2, 'TEL006', 2, 'GV040', 'BVH_VT1', 20, 0),
('2022-2023', 3, 'CSE004', 1, 'GV024', 'BVH_CNTT1', 17, 0),
('2022-2023', 3, 'CSE009', 1, 'GV010', 'BVH_CNTT1', 18, 0),
('2022-2023', 3, 'SEC003', 1, 'GV045', 'BVH_ATTT', 19, 1),
('2022-2023', 3, 'PHI004', 1, 'GV073', 'BVS_CB2', 23, 0),
('2022-2023', 3, 'TEL002', 1, 'GV039', 'BVH_VT1', 11, 0),
('2022-2023', 3, 'ELE004', 1, 'GV049', 'BVH_CB1', 15, 0),
('2022-2023', 1, 'CSE004', 1, 'GV032', 'BVH_CNTT1', 16, 0),
('2022-2023', 1, 'CSE004', 2, 'GV037', 'BVH_CNTT1', 20, 0),
('2022-2023', 1, 'CSE004', 3, 'GV015', 'BVH_CNTT1', 17, 0),
('2022-2023', 1, 'CSE009', 1, 'GV003', 'BVH_CNTT1', 13, 0),
('2022-2023', 1, 'CSE009', 2, 'GV018', 'BVH_CNTT1', 13, 0),
('2022-2023', 1, 'CSE009', 3, 'GV016', 'BVH_CNTT1', 18, 0),
('2022-2023', 1, 'SEC003', 1, 'GV046', 'BVH_ATTT', 14, 0),
('2022-2023', 1, 'PHI004', 1, 'GV074', 'BVS_CB2', 21, 0),
('2022-2023', 1, 'TEL002', 1, 'GV040', 'BVH_VT1', 16, 0),
('2022-2023', 1, 'TEL002', 2, 'GV038', 'BVH_VT1', 17, 0),
('2022-2023', 1, 'TEL002', 3, 'GV040', 'BVH_VT1', 18, 0),
('2022-2023', 1, 'ELE004', 1, 'GV050', 'BVH_CB1', 24, 0),
('2022-2023', 1, 'TEL006', 1, 'GV040', 'BVH_VT1', 20, 0),
('2022-2023', 1, 'BUS006', 1, 'GV043', 'BVH_QTKD1', 18, 0),
('2022-2023', 1, 'TEL005', 1, 'GV038', 'BVH_VT1', 19, 0),
('2022-2023', 1, 'MAT003', 1, 'GV049', 'BVH_CB1', 18, 0),
('2022-2023', 1, 'MAT003', 2, 'GV049', 'BVH_CB1', 23, 0),
('2022-2023', 1, 'MAT003', 3, 'GV049', 'BVH_CB1', 15, 0);
GO

-- INSERT LỚP TÍN CHỈ NIÊN KHÓA 2023-2024
INSERT INTO [dbo].[LOPTINCHI] ([NIENKHOA], [HOCKY], [MAMH], [NHOM], [MAGV], [MAKHOA], [SOSVTOITHIEU], [HUYLOP]) VALUES
('2023-2024', 1, 'TEL009', 1, 'GV039', 'BVH_VT1', 19, 0),
('2023-2024', 1, 'TEL009', 2, 'GV040', 'BVH_VT1', 14, 0),
('2023-2024', 1, 'TEL009', 3, 'GV038', 'BVH_VT1', 16, 0),
('2023-2024', 1, 'BUS005', 1, 'GV043', 'BVH_QTKD1', 18, 1),
('2023-2024', 1, 'BUS005', 2, 'GV044', 'BVH_QTKD1', 13, 0),
('2023-2024', 1, 'BUS005', 3, 'GV043', 'BVH_QTKD1', 19, 0),
('2023-2024', 1, 'MAT005', 1, 'GV074', 'BVS_CB2', 20, 0),
('2023-2024', 1, 'TEL008', 1, 'GV039', 'BVH_VT1', 12, 1),
('2023-2024', 1, 'SEC001', 1, 'GV046', 'BVH_ATTT', 13, 1),
('2023-2024', 1, 'CSE003', 1, 'GV036', 'BVH_CNTT1', 16, 0),
('2023-2024', 1, 'CSE003', 2, 'GV019', 'BVH_CNTT1', 12, 0),
('2023-2024', 1, 'CSE003', 3, 'GV002', 'BVH_CNTT1', 15, 0),
('2023-2024', 1, 'ELE003', 1, 'GV075', 'BVS_CB2', 19, 0),
('2023-2024', 1, 'PHI005', 1, 'GV074', 'BVS_CB2', 18, 0),
('2023-2024', 1, 'SEC005', 1, 'GV046', 'BVH_ATTT', 12, 0),
('2023-2024', 1, 'SEC005', 2, 'GV045', 'BVH_ATTT', 20, 0),
('2023-2024', 1, 'SEC005', 3, 'GV045', 'BVH_ATTT', 20, 1),
('2023-2024', 1, 'CSE011', 1, 'GV017', 'BVH_CNTT1', 14, 0),
('2023-2024', 2, 'TEL009', 1, 'GV039', 'BVH_VT1', 10, 0),
('2023-2024', 2, 'TEL009', 2, 'GV038', 'BVH_VT1', 18, 0),
('2023-2024', 2, 'TEL009', 3, 'GV040', 'BVH_VT1', 11, 0),
('2023-2024', 2, 'BUS005', 1, 'GV044', 'BVH_QTKD1', 18, 0),
('2023-2024', 2, 'MAT005', 1, 'GV050', 'BVH_CB1', 17, 0),
('2023-2024', 2, 'TEL008', 1, 'GV039', 'BVH_VT1', 10, 0),
('2023-2024', 2, 'SEC001', 1, 'GV046', 'BVH_ATTT', 19, 0),
('2023-2024', 2, 'SEC001', 2, 'GV046', 'BVH_ATTT', 10, 1),
('2023-2024', 2, 'SEC001', 3, 'GV046', 'BVH_ATTT', 17, 0),
('2023-2024', 2, 'CSE003', 1, 'GV011', 'BVH_CNTT1', 15, 0),
('2023-2024', 2, 'CSE003', 2, 'GV001', 'BVH_CNTT1', 19, 0),
('2023-2024', 2, 'CSE003', 3, 'GV024', 'BVH_CNTT1', 14, 0),
('2023-2024', 3, 'TEL009', 1, 'GV038', 'BVH_VT1', 11, 0),
('2023-2024', 3, 'BUS005', 1, 'GV044', 'BVH_QTKD1', 16, 0),
('2023-2024', 3, 'MAT005', 1, 'GV049', 'BVH_CB1', 23, 0),
('2023-2024', 3, 'TEL008', 1, 'GV038', 'BVH_VT1', 19, 0),
('2023-2024', 3, 'SEC001', 1, 'GV045', 'BVH_ATTT', 15, 1),
('2023-2024', 3, 'CSE003', 1, 'GV033', 'BVH_CNTT1', 18, 1);
GO

-- INSERT LỚP TÍN CHỈ NIÊN KHÓA 2024-2025
INSERT INTO [dbo].[LOPTINCHI] ([NIENKHOA], [HOCKY], [MAMH], [NHOM], [MAGV], [MAKHOA], [SOSVTOITHIEU], [HUYLOP]) VALUES
('2024-2025', 3, 'CSE013', 1, 'GV036', 'BVH_CNTT1', 16, 0),
('2024-2025', 3, 'CSE015', 1, 'GV019', 'BVH_CNTT1', 13, 0),
('2024-2025', 3, 'CSE005', 1, 'GV005', 'BVH_CNTT1', 13, 0),
('2024-2025', 3, 'CSE005', 2, 'GV022', 'BVH_CNTT1', 18, 0),
('2024-2025', 3, 'CSE005', 3, 'GV005', 'BVH_CNTT1', 14, 0),
('2024-2025', 1, 'CSE013', 1, 'GV004', 'BVH_CNTT1', 19, 0),
('2024-2025', 1, 'CSE015', 1, 'GV035', 'BVH_CNTT1', 12, 0),
('2024-2025', 1, 'CSE015', 2, 'GV018', 'BVH_CNTT1', 11, 0),
('2024-2025', 1, 'CSE015', 3, 'GV035', 'BVH_CNTT1', 18, 0),
('2024-2025', 1, 'CSE005', 1, 'GV018', 'BVH_CNTT1', 13, 0),
('2024-2025', 1, 'CSE005', 2, 'GV020', 'BVH_CNTT1', 16, 0),
('2024-2025', 1, 'CSE005', 3, 'GV034', 'BVH_CNTT1', 19, 0),
('2024-2025', 1, 'TEL003', 1, 'GV039', 'BVH_VT1', 13, 0),
('2024-2025', 1, 'TEL003', 2, 'GV040', 'BVH_VT1', 20, 0),
('2024-2025', 1, 'TEL003', 3, 'GV040', 'BVH_VT1', 20, 0),
('2024-2025', 1, 'CSE001', 1, 'GV016', 'BVH_CNTT1', 16, 0),
('2024-2025', 1, 'CSE001', 2, 'GV020', 'BVH_CNTT1', 14, 0),
('2024-2025', 1, 'CSE001', 3, 'GV009', 'BVH_CNTT1', 18, 0),
('2024-2025', 1, 'ENG003', 1, 'GV074', 'BVS_CB2', 19, 0),
('2024-2025', 1, 'CSE011', 1, 'GV029', 'BVH_CNTT1', 16, 0),
('2024-2025', 1, 'CSE011', 2, 'GV033', 'BVH_CNTT1', 16, 0),
('2024-2025', 2, 'CSE013', 1, 'GV004', 'BVH_CNTT1', 18, 0),
('2024-2025', 2, 'CSE013', 2, 'GV009', 'BVH_CNTT1', 13, 0),
('2024-2025', 2, 'CSE013', 3, 'GV022', 'BVH_CNTT1', 14, 0),
('2024-2025', 2, 'CSE015', 1, 'GV004', 'BVH_CNTT1', 19, 0),
('2024-2025', 2, 'CSE005', 1, 'GV022', 'BVH_CNTT1', 20, 0);
GO

PRINT 'Đã insert thành công ' + CAST(@@ROWCOUNT AS VARCHAR) + ' records vào bảng LOPTINCHI';
