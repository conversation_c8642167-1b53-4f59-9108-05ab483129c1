# =============================================
# Database Service Configuration Example
# =============================================
# Copy this file to .env and modify values as needed

# Note: Inherits database credentials from parent .env automatically

# =============================================
# SQL Server Container Settings
# =============================================

# Container Configuration
CONTAINER_NAME=${DB_CONTAINER_NAME}
MSSQL_PID=${MSSQL_PID}
PLATFORM=${PLATFORM}

# Database Internal Settings
MSSQL_COLLATION=${MSSQL_COLLATION:-Vietnamese_CI_AS}
MSSQL_MEMORY_LIMIT=3072
MSSQL_TCP_PORT=1434
MSSQL_LCID=${MSSQL_LCID:-1033}

# Performance Settings
MSSQL_AGENT_ENABLED=1
MSSQL_ENABLE_HADR=0
MSSQL_ACCEPT_EULA=Y

# File Paths (inside container)
MSSQL_DATA_DIR=/var/opt/mssql/data
MSSQL_LOG_DIR=/var/opt/mssql/log
MSSQL_BACKUP_DIR=/var/backups
MSSQL_DUMP_DIR=/var/dumps

# Security Settings
MSSQL_RPC_PORT=135
MSSQL_TCP_ENABLED=1
MSSQL_NAMED_PIPE_ENABLED=0

# Backup Settings
BACKUP_COMPRESSION=true
BACKUP_CHECKSUM=true
BACKUP_INIT=true

# Health Check Configuration
HEALTHCHECK_INTERVAL=30s
HEALTHCHECK_TIMEOUT=10s
HEALTHCHECK_RETRIES=3
HEALTHCHECK_START_PERIOD=60s

# Development Settings
MSSQL_ENABLE_EXTERNAL_SCRIPTS=0
MSSQL_TELEMETRY_ENABLED=0

# Volume Mappings (host paths)
HOST_DATA_DIR=./data
HOST_LOG_DIR=./logs
HOST_BACKUP_DIR=./backups 