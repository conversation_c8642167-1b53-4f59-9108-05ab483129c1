-- Create SQL Server Logins for QLDSV_HTC roles: <PERSON><PERSON><PERSON>, <PERSON>H<PERSON>, SV

USE [master];
GO

-- Drop and recreate main application login
IF EXISTS (SELECT * FROM sys.server_principals WHERE name = '$(MSSQL_APP_USER)')
    DROP LOGIN [$(MSSQL_APP_USER)];
CREATE LOGIN [$(MSSQL_APP_USER)] WITH PASSWORD = '$(MSSQL_APP_PASSWORD)';
GO

-- Drop and recreate PGV login (full administrative access)
IF EXISTS (SELECT * FROM sys.server_principals WHERE name = '$(MSSQL_PGV_USER)')
    DROP LOGIN [$(MSSQL_PGV_USER)];
CREATE LOGIN [$(MSSQL_PGV_USER)] WITH PASSWORD = '$(MSSQL_PGV_PASSWORD)';
GO

-- Drop and recreate KHOA login (restricted access)
IF EXISTS (SELECT * FROM sys.server_principals WHERE name = '$(MSSQL_KHOA_USER)')
    DROP LOGIN [$(MSSQL_KHOA_USER)];
CREATE LOGIN [$(MSSQL_KHOA_USER)] WITH PASSWORD = '$(MSSQL_KHOA_PASSWORD)';
GO

-- Drop and recreate SV login (minimal access)
IF EXISTS (SELECT * FROM sys.server_principals WHERE name = '$(MSSQL_SV_USER)')
    DROP LOGIN [$(MSSQL_SV_USER)];
CREATE LOGIN [$(MSSQL_SV_USER)] WITH PASSWORD = '$(MSSQL_SV_PASSWORD)';
GO
