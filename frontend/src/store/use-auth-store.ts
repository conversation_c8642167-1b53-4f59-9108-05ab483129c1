import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, UserRole } from '@/types';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  login: (user: User, token: string) => void;
  logout: () => void;
}

// Mock user data for demo purposes
const mockUsers: Record<string, User> = {
  'pgv_user': {
    id: 'pgv_user',
    username: 'pgv_user',
    ho: 'Phòng',
    ten: 'Gi<PERSON>o Vụ',
    role: UserRole.PGV
  },
  'khoa_user': {
    id: 'khoa_user',
    username: 'khoa_user',
    ho: 'Khoa',
    ten: 'CNTT',
    role: UserRole.KHOA,
    makhoa: 'CNTT'
  },
  'GV045': {
    id: 'GV045',
    username: 'GV045',
    ho: 'Tr<PERSON><PERSON> Văn',
    ten: '<PERSON>h',
    role: UserRole.PGV,
    magv: 'GV045',
    makhoa: 'CNTT'
  },
  'N21DCCN064': {
    id: 'N21DCCN064',
    username: 'N21DCCN064',
    ho: 'Nguyễn Văn',
    ten: 'Sinh',
    role: UserRole.SV,
    masv: 'N21DCCN064',
    malop: 'D21CQCN01'
  }
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      
      setUser: (user) => set({ user }),
      setToken: (token) => set({ token }),
      setIsLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      
      login: (user, token) => {
        // For demo purposes, we'll use mock users
        const mockUser = mockUsers[user.id];
        if (mockUser) {
          set({
            user: mockUser,
            token,
            isAuthenticated: true,
            error: null
          });
        } else {
          set({
            error: 'Invalid credentials',
            isAuthenticated: false
          });
        }
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null
        });
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
); 