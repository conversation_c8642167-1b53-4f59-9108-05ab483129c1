import { User, UserRole } from '@/types';
import { API_BASE_URL } from '@/lib/config';

export const LOGIN_ENDPOINT = `${API_BASE_URL}/auth/login`;

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResponse {
  user: User;
  token: string;
}

// Mock users for demo
const mockUsers: Record<string, { user: User, password: string }> = {
  'pgv_user': {
    user: {
      id: 'pgv_user',
      username: 'pgv_user',
      ho: 'Phòng',
      ten: 'Gi<PERSON>o Vụ',
      role: UserRole.PGV
    },
    password: 'PGV@123456'
  },
  'khoa_user': {
    user: {
      id: 'khoa_user',
      username: 'khoa_user',
      ho: 'Khoa',
      ten: 'CNTT',
      role: UserRole.KHOA,
      makhoa: 'CNTT'
    },
    password: 'KHOA@123456'
  },
  'GV045': {
    user: {
      id: 'GV045',
      username: 'GV045',
      ho: 'Trần Văn',
      ten: 'Anh',
      role: UserRole.PGV,
      magv: 'GV045',
      makhoa: 'CNTT'
    },
    password: 'GV045pass123#'
  },
  'N21DCCN064': {
    user: {
      id: 'N21DCCN064',
      username: 'N21DCCN064',
      ho: 'Nguyễn Văn',
      ten: 'Sinh',
      role: UserRole.SV,
      masv: 'N21DCCN064',
      malop: 'D21CQCN01'
    },
    password: '123456'
  }
};

/**
 * Simulates a login API call
 * @param credentials User credentials
 * @returns Promise with user data and token
 */
export async function login(credentials: LoginCredentials): Promise<LoginResponse> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const { username, password } = credentials;
  const mockUser = mockUsers[username];
  
  if (mockUser && mockUser.password === password) {
    return {
      user: mockUser.user,
      token: 'mock-jwt-token-' + Math.random().toString(36).substring(2)
    };
  }
  
  throw new Error('Invalid credentials');
}

/**
 * Simulates a logout API call
 */
export async function logout(): Promise<void> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // In a real app, we would invalidate the token on the server
  return;
}

/**
 * Simulates getting the current user profile
 * @param token JWT token
 * @returns Promise with user data
 */
export async function getCurrentUser(token: string): Promise<User> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, we would decode the token or make an API call
  // For demo, we'll just return a mock user based on the token
  const userId = token.split('-').pop();
  
  if (!userId) {
    throw new Error('Invalid token');
  }
  
  // Just return the first mock user for demo
  return mockUsers['pgv_user'].user;
}

/**
 * Checks if a token is valid
 * @param token JWT token
 * @returns Promise with boolean indicating if token is valid
 */
export async function validateToken(token: string): Promise<boolean> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // In a real app, we would validate the token on the server
  // For demo, we'll just return true if the token exists
  return !!token && token.startsWith('mock-jwt-token-');
} 