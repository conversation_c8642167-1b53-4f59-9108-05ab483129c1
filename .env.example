# =============================================
# QLDSV-HTC Environment Configuration
# =============================================

# Project Information
PROJECT_NAME=QLDSV-HTC
PROJECT_VERSION=1.0.0

# =============================================
# Database Configuration
# =============================================

# Connection Settings
DB_HOST=localhost
DB_PORT=1434
DB_NAME=QLDSV_HTC
DB_DRIVER=ODBC Driver 18 for SQL Server

# Container Settings
DB_CONTAINER_NAME=qldsv-sqlserver
DOCKER_NETWORK=qldsv-network
MSSQL_PID=Developer
PLATFORM=linux/amd64

# Security Credentials
MSSQL_SA_PASSWORD=P@ssw0rd!QLDSV
MSSQL_APP_USER=app_user
MSSQL_APP_PASSWORD=App@123456
MSSQL_PGV_USER=pgv_user
MSSQL_PGV_PASSWORD=PGV@123456
MSSQL_KHOA_USER=khoa_user
MSSQL_KHOA_PASSWORD=KHOA@123456
MSSQL_SV_USER=sv_user
MSSQL_SV_PASSWORD=SV@123456

# Health Check Settings
HEALTH_CHECK_INTERVAL=10s
HEALTH_CHECK_TIMEOUT=5s
HEALTH_CHECK_RETRIES=5

# =============================================
# Backend Configuration
# =============================================

# Server Settings
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
BACKEND_API_URL=http://localhost:8000

# JWT Settings
JWT_SECRET=your-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION=60

# Logging
LOG_LEVEL=DEBUG

# =============================================
# Frontend Configuration
# =============================================

# Server Settings
FRONTEND_HOST=localhost
FRONTEND_PORT=5173
FRONTEND_URL=http://localhost:5173

# Frontend API Base URL
VITE_API_BASE_URL=http://localhost:8000/api/v1

VITE_API_URL=http://localhost:8000 